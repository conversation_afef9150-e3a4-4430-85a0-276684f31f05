// Composable for consistent data-cy attribute generation
import { inject, getCurrentInstance } from 'vue';

export function useDataCy() {
  // Try to get the injected dataCy function from the plugin
  const injectedDataCy = inject('dataCy', null);
  
  // Get current component instance for automatic naming
  const instance = getCurrentInstance();
  const componentName = instance?.type?.name || instance?.type?.__name || 'component';

  // Main function to generate data-cy attributes
  const dataCy = (identifier, suffix = '') => {
    // Use injected function if available (from plugin)
    if (injectedDataCy) {
      return injectedDataCy(identifier, suffix);
    }
    
    // Fallback implementation
    if (!identifier) return {};
    
    const fullIdentifier = suffix ? `${identifier}-${suffix}` : identifier;
    return { 'data-cy': fullIdentifier };
  };

  // Generate data-cy for component elements
  const componentDataCy = (elementType, suffix = '') => {
    const baseId = `${componentName}-${elementType}`.toLowerCase();
    const fullId = suffix ? `${baseId}-${suffix}` : baseId;
    return { 'data-cy': fullId.replace(/[^a-z0-9-]/g, '-') };
  };

  // Generate data-cy for form fields
  const formDataCy = (fieldName, formName = componentName) => {
    const id = `${formName}-${fieldName}`.toLowerCase().replace(/[^a-z0-9-]/g, '-');
    return { 'data-cy': id };
  };

  // Generate data-cy for buttons with actions
  const buttonDataCy = (action, context = '') => {
    const parts = [componentName, 'btn', action];
    if (context) parts.push(context);
    const id = parts.join('-').toLowerCase().replace(/[^a-z0-9-]/g, '-');
    return { 'data-cy': id };
  };

  // Generate data-cy for list items
  const listItemDataCy = (listName, index, action = '') => {
    const parts = [componentName, listName, 'item', index];
    if (action) parts.push(action);
    const id = parts.join('-').toLowerCase().replace(/[^a-z0-9-]/g, '-');
    return { 'data-cy': id };
  };

  // Generate data-cy for modals/dialogs
  const modalDataCy = (modalName, element = '') => {
    const parts = [componentName, 'modal', modalName];
    if (element) parts.push(element);
    const id = parts.join('-').toLowerCase().replace(/[^a-z0-9-]/g, '-');
    return { 'data-cy': id };
  };

  // Generate data-cy for cards/sections
  const sectionDataCy = (sectionName, element = '') => {
    const parts = [componentName, 'section', sectionName];
    if (element) parts.push(element);
    const id = parts.join('-').toLowerCase().replace(/[^a-z0-9-]/g, '-');
    return { 'data-cy': id };
  };

  // Utility to check if we should add data-cy (e.g., skip in production)
  const shouldAddDataCy = () => {
    return process.env.NODE_ENV !== 'production' || process.env.CYPRESS_ENABLED === 'true';
  };

  // Conditional data-cy (only adds if shouldAddDataCy returns true)
  const conditionalDataCy = (identifier, suffix = '') => {
    return shouldAddDataCy() ? dataCy(identifier, suffix) : {};
  };

  return {
    dataCy,
    componentDataCy,
    formDataCy,
    buttonDataCy,
    listItemDataCy,
    modalDataCy,
    sectionDataCy,
    conditionalDataCy,
    shouldAddDataCy
  };
}
