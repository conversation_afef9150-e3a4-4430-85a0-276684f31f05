// Vue plugin for automatically adding data-cy attributes
// This plugin provides utilities for consistent test attribute naming

export default {
  install(app, options = {}) {
    const config = {
      // Enable/disable the plugin (useful for production builds)
      enabled: options.enabled !== false,
      // Prefix for all data-cy attributes
      prefix: options.prefix || '',
      // Environment check - only add in development/test
      onlyInDev: options.onlyInDev !== false,
      ...options
    };

    // Skip if disabled or in production (when onlyInDev is true)
    if (!config.enabled || (config.onlyInDev && process.env.NODE_ENV === 'production')) {
      return;
    }

    // Global property for generating data-cy attributes
    app.config.globalProperties.$dataCy = (identifier, suffix = '') => {
      if (!identifier) return {};
      
      const fullIdentifier = suffix ? `${identifier}-${suffix}` : identifier;
      const finalValue = config.prefix ? `${config.prefix}-${fullIdentifier}` : fullIdentifier;
      
      return { 'data-cy': finalValue };
    };

    // Directive for automatic data-cy generation
    app.directive('cy', {
      beforeMount(el, binding) {
        if (!binding.value) return;
        
        const identifier = typeof binding.value === 'string' 
          ? binding.value 
          : binding.value.name || binding.value.id;
          
        if (identifier) {
          const finalValue = config.prefix ? `${config.prefix}-${identifier}` : identifier;
          el.setAttribute('data-cy', finalValue);
        }
      },
      updated(el, binding) {
        // Handle dynamic updates
        if (!binding.value) return;
        
        const identifier = typeof binding.value === 'string' 
          ? binding.value 
          : binding.value.name || binding.value.id;
          
        if (identifier) {
          const finalValue = config.prefix ? `${config.prefix}-${identifier}` : identifier;
          el.setAttribute('data-cy', finalValue);
        }
      }
    });

    // Composable for use in setup()
    app.provide('dataCy', (identifier, suffix = '') => {
      if (!identifier) return {};
      
      const fullIdentifier = suffix ? `${identifier}-${suffix}` : identifier;
      const finalValue = config.prefix ? `${config.prefix}-${fullIdentifier}` : fullIdentifier;
      
      return { 'data-cy': finalValue };
    });
  }
};

// Utility functions for consistent naming
export const generateDataCy = (componentName, elementType, index = null) => {
  const parts = [componentName, elementType];
  if (index !== null) parts.push(index);
  return parts.join('-').toLowerCase().replace(/[^a-z0-9-]/g, '-');
};

export const generateFormDataCy = (formName, fieldName) => {
  return `${formName}-${fieldName}`.toLowerCase().replace(/[^a-z0-9-]/g, '-');
};

export const generateListDataCy = (listName, action, index = null) => {
  const parts = [listName, action];
  if (index !== null) parts.push(index);
  return parts.join('-').toLowerCase().replace(/[^a-z0-9-]/g, '-');
};
