#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to automatically add data-cy attributes to Vue components
 * Usage: node scripts/add-data-cy-attributes.js [component-path]
 */

const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  // Elements that should get data-cy attributes
  targetElements: [
    'q-btn', 'q-input', 'q-select', 'q-checkbox', 'q-radio', 'q-toggle',
    'q-card', 'q-dialog', 'q-drawer', 'q-page', 'q-form', 'q-table',
    'q-list', 'q-item', 'q-tab', 'q-tab-panel', 'q-stepper', 'q-step',
    'button', 'input', 'select', 'form', 'div', 'section'
  ],
  
  // Attributes that can help identify the element's purpose
  identifyingAttributes: [
    'id', 'name', 'label', 'title', 'placeholder', 'aria-label'
  ],
  
  // Skip elements that already have data-cy
  skipExisting: true
};

/**
 * Generate a data-cy value based on element and context
 */
function generateDataCyValue(elementName, attributes, componentName) {
  // Try to find an identifying attribute
  for (const attr of CONFIG.identifyingAttributes) {
    if (attributes[attr]) {
      return kebabCase(attributes[attr]);
    }
  }
  
  // Fallback to element type + component name
  const baseName = componentName ? `${componentName}-${elementName}` : elementName;
  return kebabCase(baseName);
}

/**
 * Convert string to kebab-case
 */
function kebabCase(str) {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .replace(/[^a-zA-Z0-9-]/g, '')
    .toLowerCase();
}

/**
 * Parse Vue template to find elements that need data-cy
 */
function parseTemplate(templateContent, componentName) {
  const modifications = [];
  
  // Simple regex to find opening tags (this is a basic implementation)
  const tagRegex = /<(\w+(?:-\w+)*)\s*([^>]*?)(?:\s*\/)?>/g;
  let match;
  
  while ((match = tagRegex.exec(templateContent)) !== null) {
    const [fullMatch, elementName, attributesStr] = match;
    
    // Skip if not a target element
    if (!CONFIG.targetElements.includes(elementName)) continue;
    
    // Skip if already has data-cy
    if (CONFIG.skipExisting && attributesStr.includes('data-cy')) continue;
    
    // Parse attributes
    const attributes = parseAttributes(attributesStr);
    
    // Generate data-cy value
    const dataCyValue = generateDataCyValue(elementName, attributes, componentName);
    
    // Store modification info
    modifications.push({
      originalTag: fullMatch,
      elementName,
      dataCyValue,
      position: match.index
    });
  }
  
  return modifications;
}

/**
 * Parse attributes string into object
 */
function parseAttributes(attributesStr) {
  const attributes = {};
  const attrRegex = /(\w+(?:-\w+)*)(?:=["']([^"']*?)["'])?/g;
  let match;
  
  while ((match = attrRegex.exec(attributesStr)) !== null) {
    const [, name, value] = match;
    attributes[name] = value || true;
  }
  
  return attributes;
}

/**
 * Apply modifications to template content
 */
function applyModifications(templateContent, modifications) {
  // Sort modifications by position (reverse order to maintain positions)
  modifications.sort((a, b) => b.position - a.position);
  
  let modifiedContent = templateContent;
  
  for (const mod of modifications) {
    const { originalTag, dataCyValue, position } = mod;
    
    // Insert data-cy attribute before the closing >
    const newTag = originalTag.replace(/(\s*\/?>)$/, ` data-cy="${dataCyValue}"$1`);
    
    // Replace in content
    modifiedContent = 
      modifiedContent.slice(0, position) + 
      newTag + 
      modifiedContent.slice(position + originalTag.length);
  }
  
  return modifiedContent;
}

/**
 * Extract component name from file path or content
 */
function extractComponentName(filePath, content) {
  // Try to get from file name
  const fileName = path.basename(filePath, '.vue');
  if (fileName !== 'index') {
    return kebabCase(fileName);
  }
  
  // Try to get from script setup or export
  const nameMatch = content.match(/name:\s*['"]([^'"]+)['"]/);
  if (nameMatch) {
    return kebabCase(nameMatch[1]);
  }
  
  // Fallback to directory name
  const dirName = path.basename(path.dirname(filePath));
  return kebabCase(dirName);
}

/**
 * Process a single Vue file
 */
function processVueFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Extract template section
    const templateMatch = content.match(/<template[^>]*>([\s\S]*?)<\/template>/);
    if (!templateMatch) {
      console.log(`No template found in ${filePath}`);
      return;
    }
    
    const templateContent = templateMatch[1];
    const componentName = extractComponentName(filePath, content);
    
    // Find modifications needed
    const modifications = parseTemplate(templateContent, componentName);
    
    if (modifications.length === 0) {
      console.log(`No modifications needed for ${filePath}`);
      return;
    }
    
    // Apply modifications
    const modifiedTemplate = applyModifications(templateContent, modifications);
    const modifiedContent = content.replace(templateMatch[1], modifiedTemplate);
    
    // Write back to file
    fs.writeFileSync(filePath, modifiedContent);
    
    console.log(`✅ Added ${modifications.length} data-cy attributes to ${filePath}`);
    modifications.forEach(mod => {
      console.log(`   - ${mod.elementName}: ${mod.dataCyValue}`);
    });
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
}

/**
 * Main function
 */
function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Usage: node scripts/add-data-cy-attributes.js <component-path>');
    console.log('Example: node scripts/add-data-cy-attributes.js app/javascript/events/components/details/EventDetails.vue');
    process.exit(1);
  }
  
  const targetPath = args[0];
  
  if (!fs.existsSync(targetPath)) {
    console.error(`❌ File not found: ${targetPath}`);
    process.exit(1);
  }
  
  if (path.extname(targetPath) === '.vue') {
    processVueFile(targetPath);
  } else {
    console.error('❌ Only .vue files are supported');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  processVueFile,
  generateDataCyValue,
  kebabCase
};
