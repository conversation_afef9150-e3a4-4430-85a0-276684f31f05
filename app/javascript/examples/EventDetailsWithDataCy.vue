<template>
  <q-page padding class="q-pl-md bg-grey-2" v-bind="componentDataCy('page')">
    <q-breadcrumbs class="q-mb-md" v-bind="componentDataCy('breadcrumbs')">
      <q-breadcrumbs-el :to="{ name: 'events' }" v-bind="componentDataCy('breadcrumb', 'events')">
        Events
      </q-breadcrumbs-el>
      <q-breadcrumbs-el
        v-if="state.event.id"
        :to="{ name: 'event-details', params: { id: state.event.id } }"
        v-bind="componentDataCy('breadcrumb', 'current-event')"
      >
        {{ state.event.title }}
      </q-breadcrumbs-el>
      <q-breadcrumbs-el v-else v-bind="componentDataCy('breadcrumb', 'new-event')">
        New Event
      </q-breadcrumbs-el>
    </q-breadcrumbs>
    
    <div class="row">
      <div class="col-10 q-mx-auto">
        <StyledCard 
          color="#1976d2" 
          title="Event Information" 
          icon="event"
          v-bind="sectionDataCy('event-info')"
        >
          <!-- Event Title Field -->
          <div class="form-group required q-mb-md">
            <label for="evtitle">Event Title</label>
            <q-input
              id="evtitle"
              v-model="state.event.title"
              :readonly="isEventLive"
              :rules="[rules.required]"
              lazy-rules
              @update:model-value="setUpUrl"
              maxlength="150"
              bottom-slots
              :error="!!state.errors.title"
              :error-message="state.errors.title"
              v-bind="formDataCy('title')"
            />
          </div>

          <!-- Custom URL Section -->
          <div v-if="state.event.id" v-bind="sectionDataCy('custom-url')">
            <p class="text-weight-bold">Generate your own URL for the event here!</p>
            <p class="text-weight-bold">Type the text you would like to add to the URL below.</p>
            
            <div class="form-group q-mb-md">
              <label for="custom_url">Custom URL for event</label>
              <div class="url-input-group">
                <div class="url-prefix">
                  www.servaceevents.co.uk/event/{{ state.event.id }}/
                </div>
                <q-input
                  type="text"
                  placeholder="my_event"
                  v-model="state.event.custom_url"
                  v-bind="formDataCy('custom-url')"
                />
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="button-group q-mt-md" v-bind="sectionDataCy('actions')">
            <q-btn
              color="primary"
              label="Save Event"
              @click="saveEvent"
              :loading="saving"
              v-bind="buttonDataCy('save')"
            />
            <q-btn
              color="secondary"
              label="Cancel"
              @click="cancelEdit"
              v-bind="buttonDataCy('cancel')"
            />
            <q-btn
              v-if="state.event.id"
              color="negative"
              label="Delete Event"
              @click="deleteEvent"
              v-bind="buttonDataCy('delete')"
            />
          </div>
        </StyledCard>

        <!-- Additional sections would follow the same pattern -->
        <StyledCard 
          v-if="showAdvancedOptions"
          color="#1976d2" 
          title="Advanced Options" 
          icon="settings"
          v-bind="sectionDataCy('advanced-options')"
        >
          <!-- Advanced options content with data-cy attributes -->
        </StyledCard>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { useDataCy } from '@/composables/useDataCy';
import StyledCard from '@/common/StyledCard.vue';

// Use the data-cy composable
const {
  componentDataCy,
  formDataCy,
  buttonDataCy,
  sectionDataCy,
  modalDataCy,
  listItemDataCy
} = useDataCy();

// Your existing component logic here...
const state = reactive({
  event: {},
  errors: {}
});

const saving = ref(false);
const isEventLive = computed(() => state.event.live);
const showAdvancedOptions = ref(false);

// Methods
const saveEvent = () => {
  // Save logic
};

const cancelEdit = () => {
  // Cancel logic
};

const deleteEvent = () => {
  // Delete logic
};

const setUpUrl = () => {
  // URL setup logic
};
</script>

<style scoped>
.url-input-group {
  display: flex;
  align-items: center;
}

.url-prefix {
  background: #f5f5f5;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-right: none;
  border-radius: 4px 0 0 4px;
  font-size: 14px;
  color: #666;
}

.button-group {
  display: flex;
  gap: 12px;
}
</style>
