/**
 * ESLint rule to enforce data-cy attributes on interactive elements
 * Add this to your ESLint configuration to automatically check for missing data-cy attributes
 */

module.exports = {
  meta: {
    type: 'suggestion',
    docs: {
      description: 'Require data-cy attributes on interactive elements for testing',
      category: 'Best Practices',
      recommended: false
    },
    fixable: 'code',
    schema: [
      {
        type: 'object',
        properties: {
          requiredElements: {
            type: 'array',
            items: { type: 'string' }
          },
          exemptAttributes: {
            type: 'array',
            items: { type: 'string' }
          },
          onlyInTestFiles: {
            type: 'boolean'
          }
        },
        additionalProperties: false
      }
    ]
  },

  create(context) {
    const options = context.options[0] || {};
    const requiredElements = options.requiredElements || [
      'q-btn', 'q-input', 'q-select', 'q-checkbox', 'q-radio', 'q-toggle',
      'q-dialog', 'q-form', 'button', 'input', 'select', 'form'
    ];
    const exemptAttributes = options.exemptAttributes || ['disabled', 'readonly'];
    const onlyInTestFiles = options.onlyInTestFiles || false;

    // Skip if onlyInTestFiles is true and this isn't a test file
    if (onlyInTestFiles && !context.getFilename().includes('.spec.') && !context.getFilename().includes('.test.')) {
      return {};
    }

    return {
      'VElement'(node) {
        // Check if this is a required element
        if (!requiredElements.includes(node.rawName)) {
          return;
        }

        // Check if element already has data-cy
        const hasDataCy = node.startTag.attributes.some(attr => 
          attr.key && attr.key.name === 'data-cy'
        );

        if (hasDataCy) {
          return;
        }

        // Check if element has exempt attributes
        const hasExemptAttribute = node.startTag.attributes.some(attr => 
          attr.key && exemptAttributes.includes(attr.key.name)
        );

        if (hasExemptAttribute) {
          return;
        }

        // Report missing data-cy
        context.report({
          node: node.startTag,
          message: `Interactive element '${node.rawName}' should have a data-cy attribute for testing`,
          fix(fixer) {
            // Auto-fix by adding data-cy attribute
            const elementName = node.rawName;
            const suggestedValue = generateDataCyValue(elementName, node.startTag.attributes);
            
            // Find a good position to insert the attribute
            const lastAttr = node.startTag.attributes[node.startTag.attributes.length - 1];
            const insertPosition = lastAttr ? lastAttr.range[1] : node.startTag.range[0] + elementName.length + 1;
            
            return fixer.insertTextAfterRange(
              [insertPosition, insertPosition],
              ` data-cy="${suggestedValue}"`
            );
          }
        });
      }
    };
  }
};

/**
 * Generate a suggested data-cy value based on element attributes
 */
function generateDataCyValue(elementName, attributes) {
  // Try to find identifying attributes
  const identifyingAttrs = ['id', 'name', 'label', 'aria-label'];
  
  for (const attrName of identifyingAttrs) {
    const attr = attributes.find(a => a.key && a.key.name === attrName);
    if (attr && attr.value && attr.value.value) {
      return kebabCase(attr.value.value);
    }
  }
  
  // Fallback to element name
  return kebabCase(elementName);
}

/**
 * Convert string to kebab-case
 */
function kebabCase(str) {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .replace(/[^a-zA-Z0-9-]/g, '')
    .toLowerCase();
}
